package com.greenterp.ui.voice

import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.greenterp.data.VoiceSession
import com.greenterp.GlossaryManager
import com.greenterp.ReplaceManager
import com.greenterp.ui.components.HighlightedText
import kotlinx.coroutines.launch
import android.util.Log

/**
 * 语音会话项组件
 * 显示单个语音识别会话的内容
 */
@Composable
fun VoiceSessionItem(
    session: VoiceSession,
    isCurrentSession: Boolean = false,
    fontSize: Int = 16,
    isDarkTheme: Boolean = false,
    searchValue: String = "",
    glossaryManager: GlossaryManager? = null, // 词汇表管理器
    replaceManager: ReplaceManager? = null, // 新增：替换表管理器
    onStylusTextSelected: ((String) -> Unit)? = null, // 新增：触控笔划词回调
    highlightColor: Color = Color.Red, // 新增：高亮颜色
    modifier: Modifier = Modifier
) {
    // 每个会话区域都是一个完整的屏幕高度容器
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(if (isDarkTheme) Color(0xFF1E1E1E) else Color.White)
    ) {
        // 时间戳分隔线 - 固定在顶部
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            HorizontalDivider(
                modifier = Modifier.weight(1f),
                thickness = 1.dp,
                color = Color(0xFF07c160)
            )
            Text(
                text = session.timestamp,
                style = TextStyle(
                    fontSize = (fontSize - 2).sp,
                    lineHeight = ((fontSize - 2) * 1.4f).sp,
                    fontWeight = FontWeight.Medium
                ),
                color = Color(0xFF07c160),
                modifier = Modifier.padding(horizontal = 16.dp)
            )
            HorizontalDivider(
                modifier = Modifier.weight(1f),
                thickness = 1.dp,
                color = Color(0xFF07c160)
            )
        }

        // 识别结果区域 - 可滚动的内容区域
        if (isCurrentSession && session.recognitionResults.isEmpty()) {
            // 当前会话且没有识别结果时，时间戳已经在上面显示了，这里显示等待状态
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f), // 占据剩余空间
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "",
                    style = TextStyle(
                        fontSize = fontSize.sp,
                        lineHeight = (fontSize * 1.4f).sp
                    ),
                    color = if (isDarkTheme) Color(0xFF999999) else Color(0xFF999999),
                    textAlign = TextAlign.Center
                )
            }
        } else {
            // 有识别结果的情况 - 可滚动的内容区域
            val innerListState = rememberLazyListState()
            val coroutineScope = rememberCoroutineScope()

            // 当识别结果更新时，自动滚动到底部（监听所有变化，包括中间结果）
            LaunchedEffect(session.recognitionResults.hashCode()) {
                if (session.recognitionResults.isNotEmpty() && isCurrentSession) {
                    // 延迟确保布局完成
                    kotlinx.coroutines.delay(50)
                    coroutineScope.launch {
                        // 使用合理的偏移量，足够大但不会导致闪烁
                        innerListState.animateScrollToItem(
                            index = session.recognitionResults.size - 1,
                            scrollOffset = 10000 // 使用10000像素偏移，通常足够滚动到item底部
                        )
                    }
                }
            }


            LazyColumn(
                state = innerListState,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f), // 占据剩余空间
                contentPadding = PaddingValues(top = 10.dp, bottom = 10.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                if (session.recognitionResults.isEmpty()) {
                    item {
                        Text(
                            text = "...",
                            style = TextStyle(
                                fontSize = fontSize.sp,
                                lineHeight = (fontSize * 1.4f).sp
                            ),
                            color = if (isDarkTheme) Color(0xFF999999) else Color(0xFF999999),
                            modifier = Modifier.padding(horizontal = 16.dp)
                        )
                    }
                } else {
                    // 调试信息
                    println("Displaying ${session.recognitionResults.size} results for session ${session.timestamp}")

                    items(session.recognitionResults.size) { index ->
                        val result = session.recognitionResults[index]

                        // 参考JS逻辑：先处理替换表，再处理词汇表
                        var processedText = result.transcript

                        // 1. 先应用替换表处理（直接在文本中添加加粗标记）
                        if (replaceManager != null) {
                            processedText = applyReplaceTable(processedText, replaceManager, "")
                        }

                        // 2. 再应用词汇表处理（在替换后的文本基础上）
                        val finalProcessedText = if (glossaryManager != null) {
                            glossaryManager.processAsrText(processedText)
                        } else {
                            com.greenterp.ProcessedText(processedText, emptyList())
                        }

                        SelectionContainer {
                            HighlightedText(
                                processedText = finalProcessedText,
                                fontSize = fontSize.sp,
                                normalColor = if (result.isIntermediate) {
                                    // 中间结果使用较淡的颜色
                                    if (isDarkTheme) Color.White else Color.Black
                                } else {
                                    // 最终结果使用正常颜色
                                    if (isDarkTheme) Color.White else Color.Black
                                },
                                highlightColor = highlightColor, // 使用传入的高亮颜色
                                // replaceHighlightColor = Color.Blue, // 替换表蓝色高亮 - 备用代码
                                onStylusTextSelected = onStylusTextSelected, // 传递触控笔划词回调
                                modifier = Modifier
                                    .padding(horizontal = 16.dp)
                                    .fillMaxWidth()
                                    .pointerInput(Unit) {
                                        detectTapGestures(
                                            onLongPress = {
                                                // 长按处理逻辑
                                            }
                                        )
                                    }
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 应用替换表处理，直接在文本中添加加粗标记（参考JS逻辑）
 * @param text 原始文本
 * @param replaceManager 替换表管理器
 * @param targetLanguage 目标语言
 * @return 处理后的文本（包含加粗标记）
 */
private fun applyReplaceTable(
    text: String,
    replaceManager: com.greenterp.ReplaceManager,
    targetLanguage: String
): String {
    var processedText = text

    // 获取替换表数据
    val replaceList = replaceManager.replaceItems.value

    for (replaceItem in replaceList) {
        val replaceA = replaceItem.replaceA
        val replaceB = replaceItem.replaceB

        // 执行替换并添加加粗标记（类似JS中的 <b>${B}</b>）
        val regex = Regex(replaceA, RegexOption.IGNORE_CASE)
        if (regex.containsMatchIn(processedText)) {
            // 使用特殊标记来表示加粗，稍后在HighlightedText中处理
            processedText = regex.replace(processedText, "**$replaceB**")
            Log.d("VoiceSessionItem", "替换表匹配: '$replaceA' -> '**$replaceB**'")
        }
    }

    return processedText
}
