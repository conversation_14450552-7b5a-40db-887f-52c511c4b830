package com.greenterp.ui.translation

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.greenterp.R
import com.greenterp.data.VoiceSession
import com.greenterp.data.TranslationSession
import com.greenterp.ui.voice.VoiceRecognitionDisplay
import com.greenterp.GlossaryManager

/**
 * 语音识别与翻译分屏显示组件
 * 支持上下分割布局，可拖拽调整比例和交换位置
 */
@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun SplitVoiceAndTranslateDisplay(
    sessions: List<VoiceSession>,
    fontSize: Int,
    isDarkTheme: Boolean,
    searchValue: String,
    splitRatio: Float,
    onSplitRatioChange: (Float) -> Unit,
    listState: LazyListState,
    // 使用翻译会话参数
    translationSessions: List<TranslationSession> = emptyList(),
    translationListState: LazyListState,
    glossaryManager: GlossaryManager? = null, // 新增：词汇表管理器
    highlightColor: Color = Color.Red, // 新增：高亮颜色
    modifier: Modifier = Modifier
) {
    var currentSplitRatio by remember { mutableStateOf(splitRatio) }
    var isSwapped by remember { mutableStateOf(false) }
    val density = LocalDensity.current

    // 当外部 splitRatio 改变时，更新内部状态
    LaunchedEffect(splitRatio) {
        currentSplitRatio = splitRatio
    }

    BoxWithConstraints(
        modifier = modifier
    ) {
        val maxHeight = maxHeight
        val maxHeightPx = with(density) { maxHeight.toPx() }

        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // 上部分：根据 isSwapped 决定显示内容
            if (!isSwapped) {
                // 正常模式：上部分显示语音识别
                VoiceRecognitionDisplay(
                    sessions = sessions,
                    fontSize = fontSize,
                    isDarkTheme = isDarkTheme,
                    searchValue = searchValue,
                    glossaryManager = glossaryManager, // 传递词汇表管理器
                    replaceManager = replaceManager, // 传递替换表管理器
                    onStylusTextSelected = onStylusTextSelected, // 传递触控笔划词回调
                    highlightColor = highlightColor, // 传递高亮颜色
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(maxHeight * currentSplitRatio),
                    listState = listState
                )
            } else {
                // 交换模式：上部分显示翻译区域
                TranslationArea(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(maxHeight * currentSplitRatio),
                    isDarkTheme = isDarkTheme,
                    fontSize = fontSize,
                    sessions = translationSessions,
                    listState = translationListState
                )
            }

            // 分割线和转换按钮
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(32.dp), // 增加高度以容纳按钮
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 分割线
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .height(4.dp)
                        .background(Color.Gray)
                        .pointerInput(Unit) {
                            detectDragGestures { change, dragAmount ->
                                val dragY = dragAmount.y
                                val newRatio = currentSplitRatio + (dragY / maxHeightPx)
                                currentSplitRatio = newRatio.coerceIn(0.1f, 0.9f)
                                onSplitRatioChange(currentSplitRatio)
                            }
                        }
                )

                // 转换按钮
                IconButton(
                    onClick = { isSwapped = !isSwapped },
                    modifier = Modifier
                        .size(28.dp) // 增加按钮大小
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.swap_vert_24px),
                        contentDescription = "Swap", // 添加描述
                        tint = Color(0xFF07c160), // 确保图标是白色
                        modifier = Modifier.size(24.dp) // 调整图标大小
                    )
                }
            }

            // 下部分：根据 isSwapped 决定显示内容
            if (!isSwapped) {
                // 正常模式：下部分显示翻译区域
                TranslationArea(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    isDarkTheme = isDarkTheme,
                    fontSize = fontSize,
                    sessions = translationSessions,
                    listState = translationListState
                )
            } else {
                // 交换模式：下部分显示语音识别
                VoiceRecognitionDisplay(
                    sessions = sessions,
                    fontSize = fontSize,
                    isDarkTheme = isDarkTheme,
                    searchValue = searchValue,
                    glossaryManager = glossaryManager, // 传递词汇表管理器
                    replaceManager = replaceManager, // 传递替换表管理器
                    onStylusTextSelected = onStylusTextSelected, // 传递触控笔划词回调
                    highlightColor = highlightColor, // 传递高亮颜色
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    listState = listState
                )
            }
        }
    }
}
