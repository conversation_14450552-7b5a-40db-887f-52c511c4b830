package com.greenterp.ui.translation

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.greenterp.data.TranslationSession
import kotlinx.coroutines.launch

@Composable
fun TranslationSessionItem(
    session: TranslationSession,
    isCurrentSession: Boolean = false,
    fontSize: Int = 16,
    isDarkTheme: Boolean = false,
    modifier: Modifier = Modifier
) {
    // 每个翻译会话区域 - 与VoiceSessionItem样式一致
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(if (isDarkTheme) Color(0xFF1E1E1E) else Color.White)
    ) {
        // 时间戳分隔线 - 与VoiceSessionItem一致的样式
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            HorizontalDivider(
                modifier = Modifier.weight(1f),
                thickness = 1.dp,
                color = Color(0xFF07c160)
            )
            Text(
                text = session.timestamp,
                style = TextStyle(
                    fontSize = (fontSize - 2).sp,
                    lineHeight = ((fontSize - 2) * 1.4f).sp,
                    fontWeight = FontWeight.Medium
                ),
                color = Color(0xFF07c160),
                modifier = Modifier.padding(horizontal = 16.dp)
            )
            HorizontalDivider(
                modifier = Modifier.weight(1f),
                thickness = 1.dp,
                color = Color(0xFF07c160)
            )
        }

        // 翻译结果区域 - 可滚动的内容区域
        if (isCurrentSession && session.translationResults.isEmpty()) {
            // 当前会话且没有翻译结果时，显示等待状态
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(100.dp), // 固定高度
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "",
                    style = TextStyle(
                        fontSize = fontSize.sp,
                        lineHeight = (fontSize * 1.4f).sp
                    ),
                    color = if (isDarkTheme) Color(0xFF999999) else Color(0xFF999999),
                    textAlign = TextAlign.Center
                )
            }
        } else {
            // 有翻译结果的情况 - 可滚动的内容区域
            val innerListState = rememberLazyListState()
            val coroutineScope = rememberCoroutineScope()

            // 当翻译结果更新时，自动滚动到底部
            LaunchedEffect(session.translationResults.hashCode()) {
                if (session.translationResults.isNotEmpty() && isCurrentSession) {
                    // 延迟确保布局完成
                    kotlinx.coroutines.delay(50)
                    coroutineScope.launch {
                        // 使用合理的偏移量，足够大但不会导致闪烁
                        innerListState.animateScrollToItem(
                            index = session.translationResults.size - 1,
                            scrollOffset = 10000 // 使用10000像素偏移，通常足够滚动到item底部
                        )
                    }
                }
            }


            LazyColumn(
                state = innerListState,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f), // 占据剩余空间
                contentPadding = PaddingValues(top = 10.dp, bottom = 1.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                if (session.translationResults.isEmpty()) {
                    item {
                        Text(
                            text = "...",
                            style = TextStyle(
                                fontSize = fontSize.sp,
                                lineHeight = (fontSize * 1.4f).sp
                            ),
                            color = if (isDarkTheme) Color(0xFF999999) else Color(0xFF999999),
                            modifier = Modifier.padding(horizontal = 16.dp)
                        )
                    }
                } else {
                    items(session.translationResults.size) { index ->
                        val result = session.translationResults[index]
                        TranslationResultItem(
                            result = result,
                            fontSize = fontSize,
                            isDarkTheme = isDarkTheme
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun TranslationResultItem(
    result: com.greenterp.data.TranslationResult,
    fontSize: Int,
    isDarkTheme: Boolean
) {
    // 翻译文本 - 与VoiceSessionItem中的文本样式一致
    SelectionContainer {
        Text(
            text = result.translatedText,
            fontSize = fontSize.sp,
            color = if (result.isIntermediate) {
                // 中间结果使用较淡的颜色
                if (isDarkTheme) Color(0xFFCCCCCC) else Color(0xFF666666)
            } else {
                // 最终结果使用正常颜色
                if (isDarkTheme) Color.White else Color.Black
            },
            lineHeight = (fontSize * 1.4f).sp, // 与其他组件一致的行高
            modifier = Modifier
                .padding(horizontal = 16.dp) // 与VoiceSessionItem一致的水平边距
                .fillMaxWidth()
        )
    }
}
